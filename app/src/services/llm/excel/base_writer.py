"""
Base Excel writer with common functionality.

This module provides the base class for all Excel writers with common
utilities and data extraction methods.
"""

from abc import ABC, abstractmethod
from typing import Any, Dict, Union, List

from openpyxl.workbook import Workbook

from .styles import auto_adjust_column_widths


class BaseExcelWriter(ABC):
    """
    Abstract base class for Excel writers.
    
    Provides common functionality for data extraction and file operations.
    """
    
    @abstractmethod
    async def write_excel_file(
        self, 
        campaign_data: Dict[str, Any], 
        output_format: Dict[str, Any], 
        file_path: str
    ) -> None:
        """
        Write Excel file for the specific platform.
        
        Args:
            campaign_data: Campaign data dictionary
            output_format: Output format configuration
            file_path: Path to save the Excel file
        """
        pass
    
    @staticmethod
    def get_field_value(field_data: Any, default: Any = "") -> Any:
        """
        Extract field value from data structure.
        
        Args:
            field_data: Field data (can be dict with 'value' key or direct value)
            default: Default value if field is None or empty
            
        Returns:
            Extracted field value
        """
        if isinstance(field_data, dict) and "value" in field_data:
            return field_data.get("value", default)
        return field_data if field_data is not None else default
    
    def create_workbook(self, title: str) -> tuple[Workbook, Any]:
        """
        Create a new workbook with a worksheet.
        
        Args:
            title: Worksheet title
            
        Returns:
            Tuple of (workbook, worksheet)
        """
        wb = Workbook()
        ws = wb.active
        ws.title = title
        return wb, ws
    
    def save_workbook(self, wb: Workbook, file_path: str) -> None:
        """
        Save workbook with auto-adjusted column widths.
        
        Args:
            wb: Workbook to save
            file_path: Path to save the file
        """
        # Auto-adjust column widths for all worksheets
        for ws in wb.worksheets:
            auto_adjust_column_widths(ws)
        
        wb.save(file_path)
    
    def ensure_list(self, value: Any, max_items: int = None) -> List[Any]:
        """
        Ensure value is a list with optional max items limit.
        
        Args:
            value: Value to convert to list
            max_items: Maximum number of items to return
            
        Returns:
            List representation of the value
        """
        if not isinstance(value, list):
            result = [value] if value else []
        else:
            result = value
        
        if max_items is not None:
            result = result[:max_items]
        
        return result
    
    def get_nested_value(self, data: Dict[str, Any], path: str, default: Any = "") -> Any:
        """
        Get nested value from dictionary using dot notation.
        
        Args:
            data: Dictionary to search
            path: Dot-separated path (e.g., "ads.meta.headline")
            default: Default value if path not found
            
        Returns:
            Value at the specified path
        """
        keys = path.split(".")
        current = data
        
        for key in keys:
            if isinstance(current, dict) and key in current:
                current = current[key]
            else:
                return default
        
        return self.get_field_value(current, default)
